{"name": "skateshop", "version": "0.1.0", "private": true, "type": "module", "scripts": {"clean": "rimraf --glob **/node_modules **/dist **/.next pnpm-lock.yaml **/.tsbuildinfo **/.contentlayer **/.react-email", "build": "contentlayer build && next build", "dev": "next dev", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "contentlayer build && tsc --noEmit", "format:write": "prettier --write \"**/*.{ts,tsx,mdx}\" --cache", "format:check": "prettier --check \"**/*.{ts,tsx,mdx}\" --cache", "check": "pnpm lint && pnpm typecheck && pnpm format:check", "shadcn:add": "pnpm dlx shadcn-ui@latest add", "db:generate": "dotenv drizzle-kit generate", "db:introspect": "dotenv drizzle-kit introspect", "db:push": "dotenv drizzle-kit push", "db:migrate": "dotenv tsx src/db/migrate.ts", "db:drop-migration": "drizzle-kit drop", "db:seed": "dotenv tsx src/db/seed.ts", "db:studio": "dotenv drizzle-kit studio", "email:dev": "email dev --dir src/components/emails -p 3001", "stripe:listen": "stripe listen --forward-to localhost:3000/api/webhooks/stripe --latest", "unlighthouse": "pnpm dlx unlighthouse --site https://skateshop.sadmn.com"}, "dependencies": {"@clerk/nextjs": "^5.2.3", "@clerk/themes": "^2.1.10", "@hookform/resolvers": "^3.9.0", "@loglib/tracker": "^0.8.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@react-email/components": "^0.0.21", "@react-email/tailwind": "0.0.18", "@stripe/react-stripe-js": "^2.7.3", "@stripe/stripe-js": "^4.1.0", "@t3-oss/env-nextjs": "^0.10.1", "@tanstack/match-sorter-utils": "^8.15.1", "@tanstack/react-table": "^8.19.3", "@tremor/react": "^3.17.4", "@uploadthing/react": "^6.7.2", "@upstash/ratelimit": "^1.2.1", "@upstash/redis": "^1.32.0", "ai": "^3.2.22", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "contentlayer": "^0.3.4", "date-fns": "^3.6.0", "drizzle-orm": "^0.32.0", "embla-carousel-react": "8.1.6", "framer-motion": "^11.3.2", "geist": "^1.3.1", "input-otp": "^1.2.4", "nanoid": "^5.0.7", "next": "14.2.5", "next-contentlayer": "^0.3.4", "next-themes": "^0.3.0", "openai": "^4.52.7", "pg": "^8.12.0", "postgres": "^3.4.4", "react": "18.3.1", "react-cropper": "^2.3.3", "react-day-picker": "^8.10.1", "react-dom": "18.3.1", "react-dropzone": "^14.2.3", "react-email": "^2.1.5", "react-hook-form": "^7.52.1", "react-intersection-observer": "^9.13.0", "react-markdown": "^9.0.1", "react-medium-image-zoom": "^5.2.7", "react-syntax-highlighter": "^15.5.0", "react-textarea-autosize": "^8.5.3", "react-use-measure": "^2.1.1", "remark-gfm": "^4.0.0", "resend": "^3.4.0", "server-only": "^0.0.1", "sonner": "^1.5.0", "stripe": "^16.2.0", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "uploadthing": "^6.13.2", "vaul": "^0.9.1", "zod": "^3.23.8"}, "devDependencies": {"@clerk/types": "^4.6.1", "@faker-js/faker": "^8.4.1", "@ianvs/prettier-plugin-sort-imports": "^4.3.1", "@tailwindcss/typography": "^0.5.13", "@total-typescript/ts-reset": "^0.5.1", "@types/eslint": "^8.56.10", "@types/node": "^20.14.10", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-syntax-highlighter": "^15.5.13", "@typescript-eslint/eslint-plugin": "^7.16.0", "@typescript-eslint/parser": "^7.16.0", "autoprefixer": "^10.4.19", "dotenv-cli": "^7.4.2", "drizzle-kit": "^0.23.0", "eslint": "^8.57.0", "eslint-config-next": "^14.2.5", "eslint-config-prettier": "^9.1.0", "eslint-plugin-tailwindcss": "^3.17.4", "postcss": "^8.4.39", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.5", "rehype-autolink-headings": "^7.1.0", "rehype-code-titles": "^1.2.0", "rehype-pretty-code": "^0.13.2", "rehype-raw": "^7.0.0", "rehype-slug": "^6.0.0", "remark-code-import": "^1.2.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "rimraf": "^6.0.1", "shiki": "^1.10.3", "tailwindcss": "^3.4.4", "tsx": "^4.16.2", "typescript": "^5.5.3", "unist-builder": "^4.0.0", "unist-util-visit": "^5.0.0"}, "overrides": {"next-contentlayer": {"next": "$next"}}, "ct3aMetadata": {"initVersion": "7.12.1"}, "packageManager": "pnpm@9.5.0+sha512.140036830124618d624a2187b50d04289d5a087f326c9edfc0ccd733d76c4f52c3a313d4fc148794a2a9d81553016004e6742e8cf850670268a7387fc220c903"}