# This template is heavily inspired by the shadcn-ui/ui repository.
# See: https://github.com/shadcn-ui/ui/blob/main/.github/ISSUE_TEMPLATE/feature_request.yml

name: "Feature request"
description: Create a feature request for skateshop
title: "[feat]: "
labels: ["✨ enhancement"]
body:
  - type: markdown
    attributes:
      value: |
        ### Thanks for suggesting a feature request! Make sure to see if your feature request has already been suggested by searching through the existing issues. If you find a similar request, give it a thumbs up and add any additional context you have in the comments.

  - type: textarea
    id: feature-description
    attributes:
      label: Feature description
      description: Tell us about your feature request.
      placeholder: "I think this feature would be great because..."
      value: "Describe your feature request..."
    validations:
      required: true

  - type: textarea
    id: context
    attributes:
      label: Additional Context
      description: Add any other context about the feature here.
      placeholder: ex. screenshots, Stack Overflow links, forum links, etc.
      value: "Additional details here..."
    validations:
      required: false

  - type: checkboxes
    id: terms
    attributes:
      label: Before submitting
      description: By submitting this issue, you agree to follow our [Contributing Guidelines](https://github.com/sadmann7/skateshop/blob/main/CONTRIBUTING.md).
      options:
        - label: I've made research efforts and searched the documentation
          required: true
        - label: I've searched for existing issues and PRs
          required: true
