---
title: About
description: About the project and the author of the project.
---

This is an open source e-commerce store built with everything new in [Next.js](https://nextjs.org/).
The project is still in development. You can follow the progress on [Twitter](https://twitter.com/sadmann17).

## Tech stack used

- [Next.js](https://nextjs.org)
- [Tailwind CSS](https://tailwindcss.com)
- [<PERSON>](https://clerk.dev)
- [Drizzle ORM](https://orm.drizzle.team)
- [React Email](https://react.email)
- [Contentlayer](https://www.contentlayer.dev)
- [uploadthing](https://uploadthing.com)
- [Stripe](https://stripe.com)

## Features to be implemented

- [x] Authentication with **Clerk**
- [x] File uploads with **uploadthing**
- [x] Newsletter subscription with **React Email** and **Resend**
- [x] Blog using **MDX** and **Contentlayer**
- [x] ORM using **Drizzle ORM**
- [x] Database on **PlanetScale**
- [x] Validation with **Zod**
- [x] Storefront with products, categories, and subcategories
- [x] Seller and customer workflows
- [x] User subscriptions with **Stripe**
- [ ] Checkout with **Stripe Checkout**
- [ ] Admin dashboard with stores, products, orders, subscriptions, and payments

## Credits

- [OneStopShop](https://onestopshop.jackblatch.com) - For drizzle ORM implementation with planetscale, and stripe account connection for each store
- [Acme Corp](https://acme-corp.jumr.dev) - For the OAuth implementation with Clerk, and AuthLayout
- [craft.mxkaske.dev](https://craft.mxkaske.dev) - For the awesome fancy components
- [Taxonomy](https://tx.shadcn.com) - For contentlayer, and stripe subscription implementation
- [shadcn/ui](https://ui.shadcn.com) - For the awesome reusable components library

## About the author

Hi, I'm [Sadman](https://twitter.com/sadmann17). Self-taught web developer building websites with AI and [Next.js](https://nextjs.org/). My socials are below.

- [Twitter](https://twitter.com/sadmann17)
- [GitHub](https://github.com/sadmann7)
- [Discord](https://discord.com/users/sadmann7)
- [cal.com](https://cal.com/sadmann7)
