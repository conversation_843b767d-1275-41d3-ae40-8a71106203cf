import {
  revalidateItems,
  seedCategories,
  seedSubcategories,
  seedProducts,
} from "@/lib/actions/seed"
import { db } from "@/db"
import { stores } from "@/db/schema"
import { faker } from "@faker-js/faker"
import { generateId } from "@/lib/id"
import { slugify } from "@/lib/utils"

async function seedStores() {
  console.log("📝 Creating sample stores...")

  const storeData = [
    {
      id: generateId(),
      userId: "user_sample_1", // Sample user ID
      slug: "skateshop-central",
      name: "Skateshop Central",
      description: "Your one-stop shop for all skateboarding needs. From decks to wheels, we've got you covered!",
      plan: "pro" as const,
      productLimit: 100,
      tagLimit: 20,
      variantLimit: 20,
    },
    {
      id: generateId(),
      userId: "user_sample_2", // Sample user ID
      slug: "rad-skate-co",
      name: "Rad Skate Co.",
      description: "Premium skateboarding gear for the serious skater. Quality boards and accessories.",
      plan: "standard" as const,
      productLimit: 50,
      tagLimit: 10,
      variantLimit: 10,
    },
    {
      id: generateId(),
      userId: "user_sample_3", // Sample user ID
      slug: "street-wheels",
      name: "Street Wheels",
      description: "Street-focused skateboarding equipment. Perfect for urban skating adventures.",
      plan: "free" as const,
      productLimit: 10,
      tagLimit: 5,
      variantLimit: 5,
    }
  ]

  await db.delete(stores)
  await db.insert(stores).values(storeData)

  return storeData
}

async function runSeed() {
  console.log("⏳ Running seed...")

  const start = Date.now()

  await seedCategories()

  await seedSubcategories()

  const createdStores = await seedStores()

  // Seed products for each store
  for (const store of createdStores) {
    console.log(`📝 Seeding products for ${store.name}...`)
    await seedProducts({
      storeId: store.id,
      count: store.plan === "pro" ? 25 : store.plan === "standard" ? 15 : 8
    })
  }

  await revalidateItems()

  const end = Date.now()

  console.log(`✅ Seed completed in ${end - start}ms`)

  process.exit(0)
}

runSeed().catch((err) => {
  console.error("❌ Seed failed")
  console.error(err)
  process.exit(1)
})
