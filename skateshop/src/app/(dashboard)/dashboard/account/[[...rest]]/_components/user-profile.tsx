"use client"

import { User<PERSON>ro<PERSON>le as Clerk<PERSON><PERSON><PERSON><PERSON>fi<PERSON> } from "@clerk/nextjs"
import { dark } from "@clerk/themes"
import type { Theme, UserProfileProps } from "@clerk/types"
import { useTheme } from "next-themes"

const appearance: Theme = {
  variables: {
    borderRadius: "0.25rem",
  },
}

export function UserProfile({ ...props }: UserProfileProps) {
  const { theme } = useTheme()

  return (
    <ClerkUserProfile
      appearance={{
        ...appearance,
        baseTheme: theme === "light" ? appearance.baseTheme : dark,
        variables: {
          ...appearance.variables,
        },
      }}
      {...props}
    />
  )
}
