# Contributing Guide

Thank you for investing your time in contributing to our project! Any contribution you make will be reflected on [skateshop](<[skateshop.sadmn.com](https://github.com/sadmann7/skateshop)>) :sparkles:.

Read our [Code of Conduct](./CODE_OF_CONDUCT.md) to keep our community approachable and respectable.

In this guide you will get an overview of the contribution workflow from opening an issue, creating a PR, reviewing, and merging the PR.

## New contributor guide

To get an overview of the project, read the [README](README.md). Here are some resources to help you get started with open source contributions:

- [How to Contribute to Open Source](https://opensource.guide/how-to-contribute/)
- [Understanding the GitHub flow](https://guides.github.com/introduction/flow/)

- [GitHub Help Documentation](https://help.github.com/)
- [GitHub Flavored Markdown](https://guides.github.com/features/mastering-markdown/)

## Getting started

### Fork the repository

Fork the project [on GitHub](https://github.com/sadmann7/skateshop)

### Clone the project

Clone your fork locally. Do not clone the original repository unless you plan to become a long-term contributor and have been given permission to do so.

```shell
git clone https://github.com/sadmann7/skateshop
cd skateshop
```

### Install dependencies

Install the project dependencies:

```shell
pnpm install
```

### Create a branch

Create and check out your feature branch:

```shell
git checkout -b my-new-feature
```

### Make changes locally

Make your changes to the codebase. See the [development guide](contributing/development.md) for more information.

### Commit your changes

Commit your changes:

```shell
git commit -m 'Add some feature'
```

### Push your changes

Push your changes to your fork:

```shell
git push -u origin my-new-feature
```

### Create a pull request

When you're finished with the changes, create a pull request, also known as a PR.

- Fill the "Ready for review" template so that we can review your PR. This template helps reviewers understand your changes as well as the purpose of your pull request.

### Issues

#### Create a new issue

If you spot a problem in the codebase that you believe needs to be fixed, or you have an idea for a new feature, take a look at the [Issues](https://github.com/sadmann7/skateshop/issues).

If you can't find an open issue addressing the problem, [open a new one](https://github.com/sadmann7/skateshop/issues/new). Be sure to include a title and clear description, as much relevant information as possible, and a code sample or an executable test case demonstrating the expected behavior that is not occurring.

#### Solve an issue

Scan through our [existing issues](https://github.com/sadmann7/skateshop/issues) to find one that interests you. You can narrow down the search using `labels` and `projects` to find issues that need attention.

Then, fork the repository, create a branch, and make your changes.

Finally, open a pull request with the changes.

### Your PR is merged

Congratulations :tada::tada: The GitHub team thanks you :sparkles:.

Once your PR is merged, your contributions will be publicly visible on the [skateshop](https://github.com/sadmann7/skateshop).

### Credits

This Contributing Guide is adapted from [GitHub docs contributing guide](https://github.com/github/docs/blob/main/CONTRIBUTING.md?plain=1).
