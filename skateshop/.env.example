# Since the ".env" file is gitignored, you can use the ".env.example" file to
# build a new ".env" file when you clone the repo. Keep this file up-to-date
# when you add new variables to `.env`.

# This file will be committed to version control, so make sure not to have any
# secrets in it. If you are cloning this repo, create a copy of this file named
# ".env" and populate it with your secrets.

# When adding additional environment variables, the schema in "/src/env.mjs"
# should be updated accordingly.

# App
# Use the production URL when deploying to production
NEXT_PUBLIC_APP_URL="http://localhost:3000"

# Database
DATABASE_URL="postgres://YOUR_POSTGRESSQL_URL"

# Clerk Auth
# pk_test, and sk_test are development keys
# For production, use pk_live, and sk_live keys (a domain is required)
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY="pk_test_"
CLERK_SECRET_KEY="sk_test_"
NEXT_PUBLIC_CLERK_SIGN_IN_URL="/signin"
NEXT_PUBLIC_CLERK_SIGN_UP_URL="/signup"

# React email (resend) 
# Resend API Key found at https://resend.com/api-keys
RESEND_API_KEY="re_"
# We need to register a domain with Resend to send emails from
# Register a domain at https://resend.com/domains
# Or we can use this email provided by resend for only testing: "<EMAIL>"
EMAIL_FROM_ADDRESS="<EMAIL>"

# uploadthing
UPLOADTHING_SECRET="sk_live_"
UPLOADTHING_APP_ID="•••••••••••••••••"

# upstash
UPSTASH_REDIS_REST_URL="https://YOUR_UPSTASH_REDIS_REST_URL"
UPSTASH_REDIS_REST_TOKEN="•••••••••••••"

# Stripe
# Stripe Publishable Key and Secret Key found at https://dashboard.stripe.com/test/apikeys
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_"
STRIPE_API_KEY="sk_test_"
# Stripe Webhook Secret found at https://dashboard.stripe.com/test/webhooks/create?endpoint_location=local
# This need to replaced with the webhook secret for your webhook endpoint in production
STRIPE_WEBHOOK_SECRET="whsec_"
# Stripe Product and Price IDs for your created products 
# found at https://dashboard.stripe.com/test/products
STRIPE_STD_MONTHLY_PRICE_ID="price_"
STRIPE_PRO_MONTHLY_PRICE_ID="price_"
