{"id": "d73b4db2-ad21-4170-b781-e00f33c9112d", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.addresses": {"name": "addresses", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": true, "notNull": true}, "line1": {"name": "line1", "type": "text", "primaryKey": false, "notNull": false}, "line2": {"name": "line2", "type": "text", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": false}, "postal_code": {"name": "postal_code", "type": "text", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "current_timestamp"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.carts": {"name": "carts", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": true, "notNull": true}, "payment_intent_id": {"name": "payment_intent_id", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "client_secret": {"name": "client_secret", "type": "text", "primaryKey": false, "notNull": false}, "items": {"name": "items", "type": "json", "primaryKey": false, "notNull": false, "default": "'null'::json"}, "closed": {"name": "closed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "current_timestamp"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.categories": {"name": "categories", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "current_timestamp"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"categories_name_unique": {"name": "categories_name_unique", "nullsNotDistinct": false, "columns": ["name"]}, "categories_slug_unique": {"name": "categories_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}}, "public.customers": {"name": "customers", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "store_connect_id": {"name": "store_connect_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "store_id": {"name": "store_id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "current_timestamp"}}, "indexes": {"customers_store_id_idx": {"name": "customers_store_id_idx", "columns": [{"expression": "store_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "customers_stripe_customer_id_idx": {"name": "customers_stripe_customer_id_idx", "columns": [{"expression": "stripe_customer_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"customers_store_id_stores_id_fk": {"name": "customers_store_id_stores_id_fk", "tableFrom": "customers", "tableTo": "stores", "columnsFrom": ["store_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"customers_store_connect_id_unique": {"name": "customers_store_connect_id_unique", "nullsNotDistinct": false, "columns": ["store_connect_id"]}, "customers_stripe_customer_id_unique": {"name": "customers_stripe_customer_id_unique", "nullsNotDistinct": false, "columns": ["stripe_customer_id"]}}}, "public.notifications": {"name": "notifications", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "referred_by": {"name": "referred_by", "type": "text", "primaryKey": false, "notNull": false}, "communication": {"name": "communication", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "newsletter": {"name": "newsletter", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "marketing": {"name": "marketing", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "current_timestamp"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"notifications_email_unique": {"name": "notifications_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "notifications_token_unique": {"name": "notifications_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}}, "public.orders": {"name": "orders", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": true, "notNull": true}, "store_id": {"name": "store_id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "items": {"name": "items", "type": "json", "primaryKey": false, "notNull": false, "default": "'null'::json"}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true, "default": "'0'"}, "stripe_payment_intent_id": {"name": "stripe_payment_intent_id", "type": "text", "primaryKey": false, "notNull": true}, "stripe_payment_intent_status": {"name": "stripe_payment_intent_status", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "address_id": {"name": "address_id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "current_timestamp"}}, "indexes": {"orders_store_id_idx": {"name": "orders_store_id_idx", "columns": [{"expression": "store_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "orders_address_id_idx": {"name": "orders_address_id_idx", "columns": [{"expression": "address_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"orders_store_id_stores_id_fk": {"name": "orders_store_id_stores_id_fk", "tableFrom": "orders", "tableTo": "stores", "columnsFrom": ["store_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "orders_address_id_addresses_id_fk": {"name": "orders_address_id_addresses_id_fk", "tableFrom": "orders", "tableTo": "addresses", "columnsFrom": ["address_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.payments": {"name": "payments", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": true, "notNull": true}, "store_id": {"name": "store_id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "stripe_account_id": {"name": "stripe_account_id", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "stripe_account_created_at": {"name": "stripe_account_created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "stripe_account_expires_at": {"name": "stripe_account_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "details_submitted": {"name": "details_submitted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "current_timestamp"}}, "indexes": {"payments_store_id_idx": {"name": "payments_store_id_idx", "columns": [{"expression": "store_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payments_store_id_stores_id_fk": {"name": "payments_store_id_stores_id_fk", "tableFrom": "payments", "tableTo": "stores", "columnsFrom": ["store_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.products": {"name": "products", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "images": {"name": "images", "type": "json", "primaryKey": false, "notNull": false, "default": "'null'::json"}, "category_id": {"name": "category_id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "subcategory_id": {"name": "subcategory_id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true, "default": "'0'"}, "original_price": {"name": "original_price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "inventory": {"name": "inventory", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "status": {"name": "status", "type": "product_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "store_id": {"name": "store_id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "current_timestamp"}}, "indexes": {"products_store_id_idx": {"name": "products_store_id_idx", "columns": [{"expression": "store_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "products_category_id_idx": {"name": "products_category_id_idx", "columns": [{"expression": "category_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "products_subcategory_id_idx": {"name": "products_subcategory_id_idx", "columns": [{"expression": "subcategory_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"products_category_id_categories_id_fk": {"name": "products_category_id_categories_id_fk", "tableFrom": "products", "tableTo": "categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "products_subcategory_id_subcategories_id_fk": {"name": "products_subcategory_id_subcategories_id_fk", "tableFrom": "products", "tableTo": "subcategories", "columnsFrom": ["subcategory_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "products_store_id_stores_id_fk": {"name": "products_store_id_stores_id_fk", "tableFrom": "products", "tableTo": "stores", "columnsFrom": ["store_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.stocks": {"name": "stocks", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": true, "notNull": true}, "product_variant_id": {"name": "product_variant_id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "current_timestamp"}}, "indexes": {"stocks_product_variant_id_idx": {"name": "stocks_product_variant_id_idx", "columns": [{"expression": "product_variant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"stocks_product_variant_id_product_variants_id_fk": {"name": "stocks_product_variant_id_product_variants_id_fk", "tableFrom": "stocks", "tableTo": "product_variants", "columnsFrom": ["product_variant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.stores": {"name": "stores", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "plan": {"name": "plan", "type": "store_plan", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'free'"}, "ends_at": {"name": "ends_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "cancel_plan_at_end": {"name": "cancel_plan_at_end", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "stripe_account_id": {"name": "stripe_account_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "product_limit": {"name": "product_limit", "type": "integer", "primaryKey": false, "notNull": true, "default": 10}, "tag_limit": {"name": "tag_limit", "type": "integer", "primaryKey": false, "notNull": true, "default": 5}, "variant_limit": {"name": "variant_limit", "type": "integer", "primaryKey": false, "notNull": true, "default": 5}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "current_timestamp"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"stores_slug_unique": {"name": "stores_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}, "stores_stripe_account_id_unique": {"name": "stores_stripe_account_id_unique", "nullsNotDistinct": false, "columns": ["stripe_account_id"]}, "stores_stripe_customer_id_unique": {"name": "stores_stripe_customer_id_unique", "nullsNotDistinct": false, "columns": ["stripe_customer_id"]}}}, "public.subcategories": {"name": "subcategories", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "category_id": {"name": "category_id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "current_timestamp"}}, "indexes": {"subcategories_category_id_idx": {"name": "subcategories_category_id_idx", "columns": [{"expression": "category_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"subcategories_category_id_categories_id_fk": {"name": "subcategories_category_id_categories_id_fk", "tableFrom": "subcategories", "tableTo": "categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"subcategories_name_unique": {"name": "subcategories_name_unique", "nullsNotDistinct": false, "columns": ["name"]}, "subcategories_slug_unique": {"name": "subcategories_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}}, "public.product_tags": {"name": "product_tags", "schema": "", "columns": {"product_id": {"name": "product_id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "tag_id": {"name": "tag_id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "current_timestamp"}}, "indexes": {"product_tags_product_id_tag_id_idx": {"name": "product_tags_product_id_tag_id_idx", "columns": [{"expression": "product_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "tag_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"product_tags_product_id_products_id_fk": {"name": "product_tags_product_id_products_id_fk", "tableFrom": "product_tags", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "product_tags_tag_id_tags_id_fk": {"name": "product_tags_tag_id_tags_id_fk", "tableFrom": "product_tags", "tableTo": "tags", "columnsFrom": ["tag_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"product_tags_pk": {"name": "product_tags_pk", "columns": ["product_id", "tag_id"]}}, "uniqueConstraints": {}}, "public.tags": {"name": "tags", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": true, "default": "'blue'"}, "store_id": {"name": "store_id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "current_timestamp"}}, "indexes": {"tags_store_id_idx": {"name": "tags_store_id_idx", "columns": [{"expression": "store_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"tags_store_id_stores_id_fk": {"name": "tags_store_id_stores_id_fk", "tableFrom": "tags", "tableTo": "stores", "columnsFrom": ["store_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"tags_name_store_id_unique": {"name": "tags_name_store_id_unique", "nullsNotDistinct": true, "columns": ["name", "store_id"]}}}, "public.product_variant_values": {"name": "product_variant_values", "schema": "", "columns": {"product_variant_id": {"name": "product_variant_id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "price": {"name": "price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "stock_id": {"name": "stock_id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "current_timestamp"}}, "indexes": {"variant_values_product_variant_id_idx": {"name": "variant_values_product_variant_id_idx", "columns": [{"expression": "product_variant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "variant_values_stock_id_idx": {"name": "variant_values_stock_id_idx", "columns": [{"expression": "stock_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"product_variant_values_product_variant_id_product_variants_id_fk": {"name": "product_variant_values_product_variant_id_product_variants_id_fk", "tableFrom": "product_variant_values", "tableTo": "product_variants", "columnsFrom": ["product_variant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "product_variant_values_stock_id_stocks_id_fk": {"name": "product_variant_values_stock_id_stocks_id_fk", "tableFrom": "product_variant_values", "tableTo": "stocks", "columnsFrom": ["stock_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"product_variant_values_pk": {"name": "product_variant_values_pk", "columns": ["product_variant_id", "value"]}}, "uniqueConstraints": {}}, "public.product_variants": {"name": "product_variants", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": true, "notNull": true}, "product_id": {"name": "product_id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "variant_id": {"name": "variant_id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "current_timestamp"}}, "indexes": {"product_variants_product_id_idx": {"name": "product_variants_product_id_idx", "columns": [{"expression": "product_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "product_variants_variant_id_idx": {"name": "product_variants_variant_id_idx", "columns": [{"expression": "variant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"product_variants_product_id_products_id_fk": {"name": "product_variants_product_id_products_id_fk", "tableFrom": "product_variants", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "product_variants_variant_id_variants_id_fk": {"name": "product_variants_variant_id_variants_id_fk", "tableFrom": "product_variants", "tableTo": "variants", "columnsFrom": ["variant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.variants": {"name": "variants", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": true, "notNull": true}, "store_id": {"name": "store_id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "current_timestamp"}}, "indexes": {"variants_store_id_idx": {"name": "variants_store_id_idx", "columns": [{"expression": "store_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"variants_store_id_stores_id_fk": {"name": "variants_store_id_stores_id_fk", "tableFrom": "variants", "tableTo": "stores", "columnsFrom": ["store_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"variants_name_store_id_unique": {"name": "variants_name_store_id_unique", "nullsNotDistinct": true, "columns": ["name", "store_id"]}}}}, "enums": {"public.product_status": {"name": "product_status", "schema": "public", "values": ["active", "draft", "archived"]}, "public.store_plan": {"name": "store_plan", "schema": "public", "values": ["free", "standard", "pro"]}}, "schemas": {}, "sequences": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}